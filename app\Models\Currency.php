<?php

namespace App\Models;

use Barryvdh\LaravelIdeHelper\Eloquent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * App\Models\Currency
 *
 * @property int $id
 * @property string $currency_name
 * @property string $currency_icon
 * @property string $currency_code
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 *
 * @method static Builder|Currency newModelQuery()
 * @method static Builder|Currency newQuery()
 * @method static Builder|Currency query()
 * @method static Builder|Currency whereCreatedAt($value)
 * @method static Builder|Currency whereCurrencyCode($value)
 * @method static Builder|Currency whereCurrencyIcon($value)
 * @method static Builder|Currency whereCurrencyName($value)
 * @method static Builder|Currency whereId($value)
 * @method static Builder|Currency whereUpdatedAt($value)
 *
 * @mixin Eloquent
 */
class Currency extends Model
{
    use HasFactory;

    public $table = 'currencies';

    /**
     * @var array
     */
    public $fillable = [
        'currency_name',
        'currency_icon',
        'currency_code',
    ];

    const JPY_CODE = 'JPY';

    /**
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'currency_name' => 'string',
        'currency_icon' => 'string',
        'currency_code' => 'string',
    ];
}
